<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LawVriksh - Digital Personal Data Protection Act</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Baskerville+Old+Face&family=Source+Sans+Pro:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --background: hsl(30, 15%, 96%);
            --foreground: hsl(30, 10%, 15%);
            --card: hsl(0, 0%, 100%);
            --card-foreground: hsl(30, 10%, 15%);
            --primary: hsl(28, 84%, 44%);
            --primary-foreground: hsl(0, 0%, 100%);
            --secondary: hsl(35, 25%, 88%);
            --secondary-foreground: hsl(30, 10%, 25%);
            --muted: hsl(30, 15%, 92%);
            --muted-foreground: hsl(30, 8%, 45%);
            --accent: hsl(35, 80%, 55%);
            --accent-foreground: hsl(0, 0%, 100%);
            --border: hsl(30, 15%, 88%);
            --input: hsl(30, 15%, 88%);
            --legal-gold: hsl(42, 88%, 55%);
            --legal-bronze: hsl(28, 65%, 35%);
            --legal-cream: hsl(45, 35%, 95%);
            --legal-text: hsl(30, 10%, 20%);
            --toolbar-bg: hsl(30, 15%, 94%);
            --sidebar-bg: hsl(0, 0%, 98%);
        }

        body {
            font-family: 'Source Sans Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: var(--background);
            color: var(--foreground);
            line-height: 1.6;
        }

        .container {
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            padding-top: 64px; /* Account for fixed header */
        }

        /* Header */
        .header {
            height: 64px;
            background-color: var(--card);
            border-bottom: 1px solid var(--border);
            display: flex;
            align-items: center;
            padding: 0 24px;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 24px;
        }

        .logo-section {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .brand-name {
            font-family: 'Baskerville Old Face', serif;
            font-weight: 600;
            font-size: 1.5rem;
            color: var(--legal-text);
        }

        .header-center {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-left: 24px;
        }

        .header-right {
            margin-left: auto;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .btn {
            padding: 8px 16px;
            border-radius: 20px;
            border: none;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-edit {
            background: var(--legal-bronze);
            color: white;
        }

        .btn-edit:hover {
            background: rgba(var(--legal-bronze), 0.9);
        }

        .btn-research {
            background: transparent;
            color: var(--legal-text);
            border: 1px solid var(--border);
        }

        .btn-research:hover {
            background: var(--muted);
        }

        .saved-indicator {
            display: flex;
            align-items: center;
            gap: 6px;
            color: #22c55e;
            font-size: 14px;
            font-weight: 500;
        }

        .user-profile {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: var(--muted);
            border: 2px solid var(--border);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .user-profile:hover {
            border-color: var(--legal-gold);
        }

        .btn-publish {
            background: var(--legal-bronze);
            color: white;
            padding: 8px 20px;
            border-radius: 20px;
            border: none;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-publish:hover {
            background: rgba(var(--legal-bronze), 0.9);
        }

        /* Main Layout */
        .main-layout {
            display: flex;
            flex: 1;
            position: relative;
        }

        /* Left Toolbar */
        .left-toolbar {
            width: 64px;
            background-color: var(--toolbar-bg);
            border-right: 1px solid var(--border);
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 16px 0;
            gap: 8px;
        }

        .tool-btn {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            border: none;
            background: transparent;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            position: relative;
        }

        .tool-btn:hover {
            background: rgba(var(--legal-gold), 0.1);
            color: var(--legal-bronze);
        }

        .tool-btn::before {
            content: attr(data-tooltip);
            position: absolute;
            left: 50px;
            background: var(--foreground);
            color: var(--card);
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            white-space: nowrap;
            opacity: 0;
            pointer-events: none;
            transition: opacity 0.3s ease;
        }

        .tool-btn:hover::before {
            opacity: 1;
        }

        /* Main Content */
        .main-content {
            flex: 1;
            overflow-y: auto;
            padding: 32px;
        }

        .content-wrapper {
            max-width: 1024px;
            margin: 0 auto;
        }

        .hero-image {
            margin-bottom: 32px;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 20px -4px rgba(var(--legal-bronze), 0.15);
        }

        .hero-image img {
            width: 100%;
            height: 256px;
            object-fit: cover;
        }

        .article {
            color: var(--legal-text);
        }

        .article h1 {
            font-family: 'Baskerville Old Face', serif;
            font-size: 2.25rem;
            font-weight: bold;
            margin-bottom: 24px;
            line-height: 1.2;
        }

        .article h2 {
            font-family: 'Baskerville Old Face', serif;
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--legal-bronze);
            margin: 32px 0 16px 0;
        }

        .article h3 {
            font-family: 'Baskerville Old Face', serif;
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--legal-bronze);
            margin: 24px 0 12px 0;
        }

        .article p {
            margin-bottom: 24px;
            line-height: 1.7;
        }

        .article ul {
            margin: 16px 0;
            padding-left: 24px;
        }

        .article li {
            margin-bottom: 8px;
        }

        /* Right Sidebar */
        .right-sidebar {
            position: fixed;
            top: 64px;
            right: 0;
            width: 320px;
            height: calc(100vh - 64px);
            background-color: var(--sidebar-bg);
            border-left: 1px solid var(--border);
            transform: translateX(0);
            transition: transform 0.3s ease;
            z-index: 500;
            overflow-y: auto;
        }

        .right-sidebar.collapsed {
            transform: translateX(100%);
        }

        .sidebar-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 24px;
            border-bottom: 1px solid var(--border);
        }

        .sidebar-title {
            font-family: 'Baskerville Old Face', serif;
            font-size: 1.125rem;
            font-weight: 600;
            color: var(--legal-text);
        }

        .close-btn {
            width: 32px;
            height: 32px;
            border: none;
            background: transparent;
            cursor: pointer;
            border-radius: 4px;
            transition: background 0.3s ease;
        }

        .close-btn:hover {
            background: var(--muted);
        }

        .sidebar-content {
            padding: 24px;
        }

        .form-group {
            margin-bottom: 24px;
        }

        .form-label {
            display: block;
            font-size: 14px;
            font-weight: 500;
            color: var(--legal-text);
            margin-bottom: 8px;
        }

        .form-input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid var(--border);
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }

        .form-input:focus {
            outline: none;
            border-color: var(--legal-gold);
            box-shadow: 0 0 0 3px rgba(var(--legal-gold), 0.1);
        }

        .form-textarea {
            min-height: 80px;
            resize: vertical;
        }

        .toggle-group {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .toggle-info {
            flex: 1;
        }

        .toggle-description {
            font-size: 12px;
            color: var(--muted-foreground);
            margin-top: 4px;
        }

        .toggle-switch {
            position: relative;
            width: 44px;
            height: 24px;
            background: var(--legal-gold);
            border-radius: 12px;
            cursor: pointer;
            transition: background 0.3s ease;
        }

        .toggle-switch::before {
            content: '';
            position: absolute;
            top: 2px;
            right: 2px;
            width: 20px;
            height: 20px;
            background: white;
            border-radius: 50%;
            transition: transform 0.3s ease;
        }

        .toggle-switch.off {
            background: var(--muted);
        }

        .toggle-switch.off::before {
            transform: translateX(-20px);
        }

        .upload-area {
            border: 2px dashed var(--border);
            border-radius: 8px;
            padding: 32px;
            text-align: center;
            background: rgba(var(--muted), 0.3);
        }

        .upload-icon {
            font-size: 32px;
            color: var(--muted-foreground);
            margin-bottom: 8px;
        }

        .upload-text {
            font-size: 14px;
            color: var(--muted-foreground);
        }

        .publish-btn {
            width: 100%;
            padding: 12px;
            background: var(--legal-bronze);
            color: white;
            border: none;
            border-radius: 8px;
            font-weight: 500;
            cursor: pointer;
            transition: background 0.3s ease;
        }

        .publish-btn:hover {
            background: rgba(var(--legal-bronze), 0.9);
        }

        /* Content adjustment when sidebar is open */
        .main-content {
            margin-right: 320px;
            transition: margin-right 0.3s ease;
        }

        .main-content.sidebar-collapsed {
            margin-right: 0;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .right-sidebar {
                width: 100%;
            }
            
            .main-content {
                margin-right: 0;
            }
            
            .main-content.sidebar-collapsed {
                margin-right: 0;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            <div class="header-left">
                <div class="logo-section">
                    <span class="brand-name">LawVriksh</span>
                </div>
                
                <div class="header-center">
                    <button class="btn btn-edit">✏️ Edit</button>
                    <button class="btn btn-research">Research</button>
                </div>
            </div>
            
            <div class="header-right">
                <div class="saved-indicator">
                    <span>✓</span>
                    <span>Saved</span>
                </div>
                <div class="user-profile"></div>
                <div class="user-profile"></div>
                <button class="btn-publish">Publish</button>
            </div>
        </header>

        <!-- Main Layout -->
        <div class="main-layout">
            <!-- Left Toolbar -->
            <div class="left-toolbar">
                <button class="tool-btn" data-tooltip="Undo">↶</button>
                <button class="tool-btn" data-tooltip="Copy">📋</button>
                <button class="tool-btn" data-tooltip="Cut">✂️</button>
                <button class="tool-btn" data-tooltip="Text formatting">Aa</button>
                <button class="tool-btn" data-tooltip="Add element">+</button>
            </div>

            <!-- Main Content -->
            <main class="main-content" id="mainContent">
                <div class="content-wrapper">
                    <!-- Hero Image -->
                    <div class="hero-image">
                        <img src="/lovable-uploads/b0385a34-b5c4-4026-bcb1-505cb0c0737e.png" 
                             alt="Lady Justice with scales" />
                    </div>

                    <!-- Article Content -->
                    <article class="article">
                        <h1>Data Privacy Isn't Optional Anymore – Understanding India's Digital Personal Data Protection Act</h1>

                        <p>In today's digital economy, data is the new oil—but if misused, it can become toxic. With increasing instances of data breaches, surveillance concerns, and privacy violations, the Indian government has taken a landmark step by enacting the Digital Personal Data Protection Act, 2023 (DPDPA).</p>
                        
                        <p>This law aims to strike a balance between the individual's right to privacy and the need for organizations to use data for legitimate purposes.</p>
                        
                        <p>Whether you're a tech startup, an HR head, or a digital service provider, this law applies to you—and the time to act is now.</p>

                        <h2>What Is the Digital Personal Data Protection Act, 2023?</h2>
                        
                        <p>The DPDPA is India's first comprehensive law dedicated solely to protecting personal data in the digital realm. It lays down the rights of individuals (called Data Principals) and the responsibilities of entities collecting data (called Data Fiduciaries).</p>
                        
                        <p>The law is designed to be technology-neutral, industry-agnostic, and applicable to both government and private entities.</p>

                        <h3>Key Features of the DPDPA</h3>
                        
                        <ul>
                            <li>Consent-based data processing</li>
                            <li>Data localization requirements</li>
                            <li>Rights of data principals</li>
                            <li>Penalties for non-compliance</li>
                            <li>Cross-border data transfer regulations</li>
                        </ul>
                    </article>
                </div>
            </main>

            <!-- Right Sidebar -->
            <div class="right-sidebar" id="rightSidebar">
                <div class="sidebar-header">
                    <h2 class="sidebar-title">Post Settings</h2>
                    <button class="close-btn" onclick="toggleSidebar()">✕</button>
                </div>

                <div class="sidebar-content">
                    <div class="form-group">
                        <label class="form-label" for="seo-title">SEO Title</label>
                        <input type="text" id="seo-title" class="form-input" />
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="seo-description">SEO Description</label>
                        <textarea id="seo-description" class="form-input form-textarea"></textarea>
                    </div>

                    <div class="form-group">
                        <div class="toggle-group">
                            <div class="toggle-info">
                                <label class="form-label">Table of Content</label>
                                <div class="toggle-description">Do you want to add table of Content?</div>
                            </div>
                            <div class="toggle-switch" id="tocToggle" onclick="toggleSwitch('tocToggle')"></div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="article-slug">Article Slug</label>
                        <input type="text" id="article-slug" class="form-input" />
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="select-tags">Select Tags</label>
                        <input type="text" id="select-tags" class="form-input" />
                    </div>

                    <div class="form-group">
                        <label class="form-label">Custom Image</label>
                        <div class="upload-area">
                            <div class="upload-icon">📁</div>
                            <div class="upload-text">Drop n Drop</div>
                        </div>
                    </div>

                    <button class="publish-btn">Publish</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        function toggleSidebar() {
            const sidebar = document.getElementById('rightSidebar');
            const mainContent = document.getElementById('mainContent');
            
            sidebar.classList.toggle('collapsed');
            mainContent.classList.toggle('sidebar-collapsed');
        }

        function toggleSwitch(id) {
            const toggle = document.getElementById(id);
            toggle.classList.toggle('off');
        }

        // Initialize toggle switch as on
        document.addEventListener('DOMContentLoaded', function() {
            // Table of content toggle is on by default (no 'off' class)
        });

        // Add smooth hover effects for buttons
        document.addEventListener('DOMContentLoaded', function() {
            const buttons = document.querySelectorAll('.btn, .tool-btn, .close-btn');
            buttons.forEach(button => {
                button.addEventListener('mouseenter', function() {
                    this.style.transform = 'scale(1.05)';
                });
                button.addEventListener('mouseleave', function() {
                    this.style.transform = 'scale(1)';
                });
            });
        });
    </script>
</body>
</html>