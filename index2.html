<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Law Vriksh - Blog Editor</title>

    <script src="https://cdn.tailwindcss.com"></script>

    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;700&family=Lora:wght@400;500;600;700&display=swap" rel="stylesheet">

    <style>
        /* Define custom color palette and fonts */
        :root {
            --bg-main: #F9F5F0;
            --bg-toolbar: #F5F1EB;
            --text-dark: #4a4a4a;
            --text-light: #6b6b6b;
            --border-color: #D1C4B5;
            --brand-brown: #5D4037;
            --brand-gold: #B8860B;
        }

        body {
            font-family: 'Inter', sans-serif;
            background-color: var(--bg-main);
            color: var(--text-dark);
        }

        h1, h2, h3, .font-serif {
            font-family: 'Lora', serif;
        }

        /* Custom toggle switch style for accuracy */
        .toggle-checkbox:checked {
            transform: translateX(100%);
            background-color: white;
            border-color: var(--brand-brown);
        }
        .toggle-checkbox:checked + .toggle-label {
            background-color: var(--brand-brown);
        }
    </style>
</head>
<body class="overflow-x-hidden">

    <div class="relative min-h-screen">
    
        <header class="header-main absolute top-0 left-0 right-0 z-10 flex justify-between items-center py-6 px-8 md:px-12">
            <div class="logo font-serif text-2xl font-bold" style="color: var(--brand-gold);">
                &#127795; Law Vriksh
            </div>
            <div class="save-status flex items-center space-x-2 text-green-600">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                </svg>
                <span class="text-sm font-medium">Saved</span>
            </div>
        </header>


        <aside class="toolbar fixed left-4 md:left-6 top-1/2 -translate-y-1/2 z-20">
            <div class="flex flex-col items-center space-y-3 p-2 rounded-full shadow-sm" style="background-color: var(--bg-toolbar); border: 1px solid var(--border-color);">
                <a href="#" class="toolbar-icon p-3 rounded-full border border-transparent hover:border-[var(--border-color)] transition-all duration-300">
                    <svg class="h-6 w-6" style="color: var(--text-dark);" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1.5"><path stroke-linecap="round" stroke-linejoin="round" d="M9.53 16.122a3 3 0 00-5.78 1.128 2.25 2.25 0 01-2.4 2.245 4.5 4.5 0 008.4-2.245c0-.399-.078-.78-.22-1.128zm0 0a15.998 15.998 0 003.388-1.62m-5.043-.025a15.998 15.998 0 011.622-3.385m5.043.025a15.998 15.998 0 001.622-3.385m3.388 1.62a15.998 15.998 0 00-1.62-3.385m-5.043-.025a15.998 15.998 0 01-3.388-1.621m-5.043.025a15.998 15.998 0 00-3.388 1.622m5.043.025a15.998 15.998 0 01-1.622 3.385m3.388-1.622a15.998 15.998 0 01-1.622 3.385M6 12a6 6 0 1112 0 6 6 0 01-12 0z" /></svg>
                </a>
                <a href="#" class="toolbar-icon p-3 rounded-full border border-transparent hover:border-[var(--border-color)] transition-all duration-300">
                    <svg class="h-6 w-6" style="color: var(--text-dark);" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1.5"><path stroke-linecap="round" stroke-linejoin="round" d="M7.5 8.25h9m-9 3H12m-9.75 3h9.75m-9.75 2.25h9.75m4.5-4.5v6.75a2.25 2.25 0 01-2.25 2.25H5.25a2.25 2.25 0 01-2.25-2.25V5.25a2.25 2.25 0 012.25-2.25h4.5m4.5 0v6.75m0 0l-3.75-3.75M17.25 9l-3.75 3.75" /></svg>
                </a>
                <a href="#" class="toolbar-icon p-3 rounded-full border border-transparent hover:border-[var(--border-color)] transition-all duration-300">
                    <svg class="h-6 w-6" style="color: var(--text-dark);" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1.5"><path stroke-linecap="round" stroke-linejoin="round" d="M10.5 6h9.75M10.5 6a1.5 1.5 0 11-3 0m3 0a1.5 1.5 0 10-3 0M3.75 6H7.5m3 12h9.75m-9.75 0a1.5 1.5 0 01-3 0m3 0a1.5 1.5 0 00-3 0m-3.75 0H7.5m9-6h3.75m-3.75 0a1.5 1.5 0 01-3 0m3 0a1.5 1.5 0 00-3 0m-9.75 0h9.75" /></svg>
                </a>
                <a href="#" class="toolbar-icon p-3 rounded-full border border-[var(--border-color)] bg-white shadow-sm">
                    <svg class="h-6 w-6" style="color: var(--text-dark);" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1.5"><path stroke-linecap="round" stroke-linejoin="round" d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09zM18.259 8.715L18 9.75l-.259-1.035a3.375 3.375 0 00-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 002.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 002.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 00-2.456 2.456zM16.898 20.572L16.5 21.75l-.398-1.178a3.375 3.375 0 00-2.455-2.456L12.75 18l1.178-.398a3.375 3.375 0 002.455-2.456L16.5 14.25l.398 1.178a3.375 3.375 0 002.456 2.456l1.178.398-1.178.398a3.375 3.375 0 00-2.456 2.456z" /></svg>
                </a>
                <div class="w-8/12 border-t mx-auto" style="border-color: var(--border-color);"></div>
                <a href="#" class="toolbar-icon p-3 rounded-full border" style="border-color: var(--border-color);">
                     <svg class="h-6 w-6" style="color: var(--text-dark);" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1.5"><path stroke-linecap="round" stroke-linejoin="round" d="M12 4.5v15m7.5-7.5h-15" /></svg>
                </a>
            </div>
        </aside>

        <div class="flex pt-24">
            <div class="w-24 flex-shrink-0"></div>

            <main class="main-content flex-grow p-8 md:p-12">
                <div class="max-w-4xl mx-auto">
                    <div class="flex items-center space-x-2 mb-8">
                        <button class="px-5 py-2 text-sm font-semibold text-white rounded-full shadow-sm" style="background-color: #333;">Edit</button>
                        <button class="px-5 py-2 text-sm font-semibold bg-white border rounded-full shadow-sm" style="border-color: var(--border-color);">Research</button>
                    </div>

                    <div class="article-image w-full h-80 rounded-lg overflow-hidden mb-8 shadow-lg">
                        <img src="https://images.unsplash.com/photo-1593112255018-493e769d1a3a?q=80&w=2671&auto=format&fit=crop" alt="Statue of Lady Justice with scales" class="w-full h-full object-cover">
                    </div>
                    
                    <article class="article-body prose lg:prose-xl max-w-none" style="color: var(--text-dark);">
                        <h1 class="text-4xl md:text-5xl !font-semibold !leading-tight" style="color: var(--text-dark);">Data Privacy Isn’t Optional Anymore – Understanding India’s Digital Personal Data</h1>
                        
                        <p class="mt-8 text-lg leading-relaxed" style="color: var(--text-light);">In today’s digital economy, data is the new oil—but if misused, it can become toxic. With increasing instances of data breaches, surveillance concerns, and privacy violations, the Indian government has taken a landmark step by enacting the Digital Personal Data Protection Act, 2023 (DPDPA).</p>
                        <p class="leading-relaxed" style="color: var(--text-light);">This law aims to strike a balance between the individual’s right to privacy and the need for organizations to use data for legitimate purposes.</p>
                        
                        <h2 class="mt-10 font-semibold text-2xl" style="color: var(--text-dark);">What is the Digital Personal Data Protection Act, 2023?</h2>
                        <p class="leading-relaxed" style="color: var(--text-light);">The DPDPA is India's first comprehensive law dedicated solely to protecting personal data in the digital realm. It lays down the rights of individuals (called Data Principals) and the responsibilities of entities collecting data (called Data Fiduciaries).</p>
                    </article>
                </div>
            </main>

            <aside class="settings-sidebar w-[380px] flex-shrink-0 p-8 border-l" style="border-color: #EAE3DA;">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-xl font-bold" style="color: var(--text-dark);">Post Settings</h2>
                    <button class="text-gray-500 hover:text-gray-800">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" /></svg>
                    </button>
                </div>
                <div class="border-t w-full mb-8" style="border-color: #EAE3DA;"></div>
                
                <div class="space-y-6">
                    <div class="setting-item">
                        <label for="seo-title" class="block text-sm font-medium mb-2">SEO Title</label>
                        <input type="text" id="seo-title" class="w-full p-2.5 bg-white border rounded-lg shadow-sm focus:ring-2 focus:ring-[var(--brand-brown)] focus:border-[var(--brand-brown)] transition" style="border-color: var(--border-color);">
                    </div>

                    <div class="setting-item">
                        <label for="seo-description" class="block text-sm font-medium mb-2">SEO Description</label>
                        <textarea id="seo-description" rows="4" class="w-full p-2.5 bg-white border rounded-lg shadow-sm focus:ring-2 focus:ring-[var(--brand-brown)] focus:border-[var(--brand-brown)] transition" style="border-color: var(--border-color);"></textarea>
                    </div>

                    <div class="setting-item">
                        <div class="flex items-center justify-between">
                           <div>
                                <label for="toc" class="text-sm font-medium">Table of Content</label>
                                <p class="text-xs" style="color: var(--text-light);">Do you want to add Table of Content?</p>
                           </div>
                            <div class="relative inline-block w-12 h-7 align-middle select-none transition duration-200 ease-in">
                                <div class="toggle-label absolute block w-12 h-7 rounded-full cursor-pointer" style="background-color: #E0E0E0;"></div>
                                <input type="checkbox" name="toggle" id="toc" class="toggle-checkbox absolute block w-5 h-5 mt-1 ml-1 rounded-full bg-white border-2 appearance-none cursor-pointer transition-transform duration-200 ease-in-out"/>
                            </div>
                        </div>
                    </div>

                    <div class="setting-item">
                        <label for="article-slug" class="block text-sm font-medium mb-2">Article Slug</label>
                        <input type="text" id="article-slug" class="w-full p-2.5 bg-white border rounded-lg shadow-sm focus:ring-2 focus:ring-[var(--brand-brown)] focus:border-[var(--brand-brown)] transition" style="border-color: var(--border-color);">
                    </div>
                    
                    <div class="setting-item">
                        <label for="tags" class="block text-sm font-medium mb-2">Select Tags</label>
                        <input type="text" id="tags" class="w-full p-2.5 bg-white border rounded-lg shadow-sm focus:ring-2 focus:ring-[var(--brand-brown)] focus:border-[var(--brand-brown)] transition" style="border-color: var(--border-color);">
                    </div>

                    <div class="setting-item">
                        <label class="block text-sm font-medium mb-2">Custom Image</label>
                        <div class="mt-1 flex justify-center items-center px-6 pt-5 pb-6 border-2 border-dashed rounded-lg" style="border-color: var(--border-color); background-color: #F0F0F0;">
                            <div class="space-y-1 text-center">
                                <svg class="mx-auto h-8 w-8 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M12 16.5V9.75m0 0l3 3m-3-3l-3 3M6.75 19.5a4.5 4.5 0 01-1.41-8.775 5.25 5.25 0 0110.233-2.33 3 3 0 013.758 3.848A3.752 3.752 0 0118 19.5H6.75z" /></svg>
                                <p class="text-xs" style="color: var(--text-light);">Drag n Drop</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="absolute bottom-8 right-8 w-[315px]">
                    <div class="pt-6 border-t" style="border-color: #EAE3DA;">
                        <button class="publish-btn w-full text-white font-bold py-3 px-4 rounded-lg shadow-md transition-all duration-300 ease-in-out transform hover:scale-105" style="background-color: var(--brand-brown);">
                            Publish
                        </button>
                    </div>
                </div>
            </aside>
        </div>
    </div>


    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.5/gsap.min.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            gsap.defaults({ ease: 'power3.out', duration: 1.2 });

            gsap.from('.header-main', { 
                y: -100,
                opacity: 0,
                delay: 0.2
            });

            gsap.from('.toolbar', { 
                x: -100, 
                opacity: 0, 
                delay: 0.4
            });

            gsap.from('.article-image', {
                scale: 0.95,
                opacity: 0,
                delay: 0.6
            });
            
            gsap.from('.article-body > *', {
                y: 40,
                opacity: 0,
                stagger: 0.15,
                delay: 0.8
            });

            gsap.from('.settings-sidebar', { 
                x: 100, 
                opacity: 0, 
                delay: 0.5
            });
            
            gsap.from('.setting-item', {
                opacity: 0,
                x: 30,
                stagger: 0.1,
                delay: 1
            });

            gsap.from('.publish-btn', {
                opacity: 0,
                y: 50,
                delay: 1.4
            });

            const toolbarIcons = document.querySelectorAll('.toolbar-icon');
            toolbarIcons.forEach(icon => {
                icon.addEventListener('mouseenter', () => gsap.to(icon, { scale: 1.15, duration: 0.3, ease: 'power2.out' }));
                icon.addEventListener('mouseleave', () => gsap.to(icon, { scale: 1, duration: 0.3, ease: 'power2.out' }));
            });
        });
    </script>
</body>
</html>